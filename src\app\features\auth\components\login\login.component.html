<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Вход в админ панель
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        ITeasy Studio
      </p>
    </div>
    
    <form class="mt-8 space-y-6" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <label for="username" class="sr-only">Имя пользователя</label>
          <input
            id="username"
            name="username"
            type="text"
            formControlName="username"
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
            [class.border-red-500]="isFieldInvalid('username')"
            placeholder="Имя пользователя"
          />
          <div *ngIf="isFieldInvalid('username')" class="text-red-500 text-xs mt-1">
            {{ getFieldError('username') }}
          </div>
        </div>
        
        <div>
          <label for="password" class="sr-only">Пароль</label>
          <input
            id="password"
            name="password"
            type="password"
            formControlName="password"
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
            [class.border-red-500]="isFieldInvalid('password')"
            placeholder="Пароль"
          />
          <div *ngIf="isFieldInvalid('password')" class="text-red-500 text-xs mt-1">
            {{ getFieldError('password') }}
          </div>
        </div>
      </div>

      <div *ngIf="errorMessage" class="text-red-500 text-sm text-center">
        {{ errorMessage }}
      </div>

      <div>
        <button
          type="submit"
          [disabled]="isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span *ngIf="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ isLoading ? 'Вход...' : 'Войти' }}
        </button>
      </div>
    </form>
  </div>
</div>
