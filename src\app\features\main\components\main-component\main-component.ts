import {Component, HostListener, OnInit, Renderer2} from '@angular/core'; // Импортируйте Renderer2
import {animate, state, style, transition, trigger} from "@angular/animations";
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title, Meta } from '@angular/platform-browser';
import { SchoolEnrollmentService } from '../../../../core/services/school-enrollment.service';
import { CreateSchoolEnrollment } from '../../../../core/models/school-enrollment.models';
import { phoneValidator } from '../../../../core/validators/phone.validator';

interface FaqItem {
  id: number;
  question: string;
  answer: string;
  isOpen: boolean;
}

@Component({
  selector: 'app-main-component',
  standalone: false,
  templateUrl: './main-component.html',
  styleUrl: './main-component.css',
  animations:
    [
      trigger('fadeInOut', [
        state('void', style({opacity: 0})),
        transition('void => *', [
          style({opacity: 0}),
          animate('200ms ease-in-out')
        ]),
        transition('* => void', [
          animate('200ms ease-in-out', style({opacity: 0}))
        ])
      ])
    ]
})
export class MainComponent implements OnInit {

  // Enrollment form properties
  enrollmentForm!: FormGroup;
  isSubmittingEnrollment = false;
  enrollmentMessage = '';
  enrollmentError = '';

  constructor(
    private renderer: Renderer2,
    private titleService: Title,
    private meta: Meta,
    private formBuilder: FormBuilder,
    private schoolEnrollmentService: SchoolEnrollmentService
  ) {}

  options = {
    path: 'assets/Animation7.json', // Путь к вашему JSON-файлу
  };

  images = [
    'assets/img/first.png',
    'assets/img/second.png',
    'assets/img/third.jpg',
    'assets/img/fourth.png',
  ];

  images2 = [
    'assets/img/2first.png',
    'assets/img/2second.jpg',
    'assets/img/2third.jpg',
    'assets/img/2fourth.png',
  ];

  images3 = [
    'assets/img/31.png',
    'assets/img/32.jpeg',
    'assets/img/33.png',
    'assets/img/34.png',
  ];

  // FAQ Accordion data
  faqItems: FaqItem[] = [
    {
      id: 1,
      question: 'Сколько стоит?',
      answer: 'В зависимости от филиала и города. Цены начинаются от 25000, вы можете узнать точную информацию у менеджера вашего города',
      isOpen: false
    },
    {
      id: 2,
      question: 'Какой график? Что с расписанием занятий?',
      answer: 'Есть группы утренние и послеобеденные, зависит от смены в школе. В неделю проводится 2 занятия с преподавателем по 1,5 часа.',
      isOpen: false
    },
    {
      id: 3,
      question: 'Со скольки лет берём?',
      answer: 'ITeasy принимает на обучение подростков от 12 лет',
      isOpen: false
    },
    {
      id: 4,
      question: 'После курсов ребенок станет программистом/ 3d-художником?',
      answer: 'Нет, мы не выпускаем готовых специалистов. Мы даём ребенку крепкий фундамент и понимание основных специальностей.',
      isOpen: false
    },
    {
      id: 5,
      question: 'Как следить за успехами ребенка?',
      answer: 'Каждый ребёнок создает своё собственное портфолио и добавляет туда свои работы. Также преподаватель группы ежемесячно отправляет отчёт по каждому ребенку лично родителю.',
      isOpen: false
    },
    {
      id: 6,
      question: 'Есть ли скидки или льготы?',
      answer: 'Скидка предоставляется только в том случае, если в ITeasy обучается два и более ребёнка из одной семьи. Размер скидки составляет 10% от стоимости обучения. Так же есть рефельная программа, вы можете уточнить информацию у менеджера вашего города',
      isOpen: false
    }
  ];

  isModalOpen = false;
  selectedImage: string | null = null;
  isLoggedIn = false;
  username?: string;


  ngOnInit() {
    this.titleService.setTitle('ITeasy School - Обучение программированию для детей');
    this.meta.updateTag({
      name: 'description',
      content: 'ITeasy School - онлайн школа программирования для детей и подростков. Изучайте Python, JavaScript, HTML/CSS, создавайте игры и веб-сайты. Индивидуальные занятия с опытными преподавателями.'
    });
    this.initializeMobileMenu();
    this.initializeEnrollmentForm();
  }

  private initializeMobileMenu(): void {
    const toggle = document.querySelector('[data-collapse-toggle="navbar-sticky"]');
    const nav = document.getElementById('navbar-sticky');

    if (toggle && nav) {
      toggle.addEventListener('click', () => {
        nav.classList.toggle('hidden');
      });
    }
  }

  openModal(image: string) {
    this.selectedImage = image;
    this.isModalOpen = true;
    this.renderer.addClass(document.body, 'no-scroll'); // Отключить прокрутку
  }

  closeModal() {
    this.isModalOpen = false;
    this.selectedImage = null;
    this.renderer.removeClass(document.body, 'no-scroll'); // Включить прокрутку
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' && this.isModalOpen) {
      this.closeModal();
    }
  }

  // Accordion functionality
  toggleAccordion(itemId: number) {
    this.faqItems = this.faqItems.map(item => ({
      ...item,
      isOpen: item.id === itemId ? !item.isOpen : item.isOpen
    }));
  }

  // TrackBy function for performance optimization
  trackByFaqId(index: number, item: FaqItem): number {
    return item.id;
  }

  // Initialize enrollment form
  private initializeEnrollmentForm(): void {
    this.enrollmentForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      phone: ['', [Validators.required, phoneValidator()]]
    });
  }

  // Method to handle enrollment form submission
  onEnrollmentSubmit(): void {
    if (this.enrollmentForm.invalid) {
      this.markEnrollmentFormTouched();
      return;
    }

    this.isSubmittingEnrollment = true;
    this.enrollmentMessage = '';
    this.enrollmentError = '';

    const enrollmentData: CreateSchoolEnrollment = {
      name: this.enrollmentForm.value.name,
      phone: this.enrollmentForm.value.phone
    };

    this.schoolEnrollmentService.createEnrollment(enrollmentData).subscribe({
      next: (response) => {
        this.isSubmittingEnrollment = false;
        this.enrollmentMessage = 'Ваша заявка на обучение успешно отправлена! Мы свяжемся с вами в ближайшее время.';
        this.enrollmentForm.reset();

        // Google Ads conversion tracking
        this.trackGoogleAdsConversion();
      },
      error: (error) => {
        this.isSubmittingEnrollment = false;
        this.handleEnrollmentError(error);
      }
    });
  }

  private handleEnrollmentError(error: any): void {
    if (error.status === 400) {
      this.enrollmentError = 'Пожалуйста, проверьте введенные данные.';
    } else if (error.status === 0) {
      this.enrollmentError = 'Ошибка подключения к серверу. Попробуйте позже.';
    } else {
      this.enrollmentError = 'Произошла ошибка при отправке заявки. Попробуйте позже.';
    }
  }

  private markEnrollmentFormTouched(): void {
    Object.keys(this.enrollmentForm.controls).forEach(key => {
      const control = this.enrollmentForm.get(key);
      control?.markAsTouched();
    });
  }

  isEnrollmentFieldInvalid(fieldName: string): boolean {
    const field = this.enrollmentForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getEnrollmentFieldError(fieldName: string): string {
    const field = this.enrollmentForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        const fieldLabels: { [key: string]: string } = {
          name: 'Имя',
          phone: 'Телефон'
        };
        return `${fieldLabels[fieldName]} обязательно для заполнения`;
      }
      if (field.errors['minlength']) {
        const minLength = field.errors['minlength'].requiredLength;
        return `Минимальная длина: ${minLength} символов`;
      }
      if (field.errors['invalidPhone']) {
        return 'Введите корректный номер телефона';
      }
    }
    return '';
  }

  // Google Ads conversion tracking
  private trackGoogleAdsConversion(): void {
    if (typeof (window as any).gtag === 'function') {
      (window as any).gtag('event', 'conversion', {
        'send_to': 'AW-17264732790/ZzD1CI-lxOlaEPbUu6hA'
      });
    }
  }

  protected readonly close = close;
}
