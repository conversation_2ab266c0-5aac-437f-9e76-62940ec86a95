# Node modules
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Angular build output
/dist
/tmp
/out-tsc
/bazel-out

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Environment files
.env
.env.local
.env.example

# Documentation
README.md
DEPLOYMENT.md

# Backend specific (for frontend build)
backend/

# SSL certificates
ssl/

# Logs
*.log

# Coverage reports
coverage/

# Test files
*.spec.ts
karma.conf.js
src/test.ts

# Angular cache
.angular/cache
