import { Component } from '@angular/core';
import { CountryService, Country } from '../../core/services/country.service';

@Component({
  selector: 'app-standalone-phone-test',
  template: `
    <div class="p-8 max-w-md">
      <h2 class="text-xl mb-4">Standalone Phone Input Test</h2>
      
      <div class="phone-input__container">
        <label class="phone-input__label">Номер телефона</label>
        
        <div class="phone-input__wrapper">
          <!-- Country Code Dropdown -->
          <div class="country-code__dropdown">
            <button 
              type="button"
              class="country-code__trigger"
              (click)="toggleDropdown($event)">
              <img 
                [src]="selectedCountry.flag" 
                [alt]="selectedCountry.name"
                class="country-code__flag-img"
                onerror="this.style.display='none'">
              <span class="country-code__value">{{ selectedCountry.dialCode }}</span>
              <span class="country-code__arrow">{{ isDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            
            <!-- Dropdown List -->
            <div 
              *ngIf="isDropdownOpen"
              class="country-code__list">
              <div 
                *ngFor="let country of countries"
                class="country-code__item"
                [class.country-code__item--active]="country.code === selectedCountry.code"
                (click)="selectCountry(country, $event)">
                <img 
                  [src]="country.flag" 
                  [alt]="country.name"
                  class="country-flag-img"
                  onerror="this.style.display='none'">
                <span class="country-dial">{{ country.dialCode }}</span>
                <span class="country-name">{{ country.name }}</span>
              </div>
            </div>
          </div>
          
          <!-- Phone Number Input -->
          <input
            type="tel"
            class="phone-input__field"
            placeholder="777 123 45 67"
            [value]="phoneNumber"
            (input)="phoneNumber = $any($event.target).value">
        </div>
      </div>
      
      <div class="mt-4 text-sm space-y-1">
        <div><strong>Dropdown Open:</strong> {{ isDropdownOpen }}</div>
        <div><strong>Selected Country:</strong> {{ selectedCountry.name }}</div>
        <div><strong>Phone Number:</strong> {{ phoneNumber }}</div>
        <div><strong>Full Number:</strong> {{ selectedCountry.dialCode }}{{ phoneNumber }}</div>
        <div><strong>Countries Loaded:</strong> {{ countries.length }}</div>
      </div>
    </div>
  `,
  styles: [`
    .phone-input__container {
      position: relative;
      width: 100%;
    }

    .phone-input__label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
      font-size: 0.875rem;
    }

    .phone-input__wrapper {
      display: flex;
      border: 1px solid #d1d5db;
      border-radius: 0.5rem;
      overflow: hidden;
      background-color: white;
    }

    .country-code__dropdown {
      position: relative;
      flex-shrink: 0;
      z-index: 1001;
    }

    .country-code__trigger {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      background: none;
      border: none;
      cursor: pointer;
      gap: 0.5rem;
      min-width: 120px;
      border-right: 1px solid #e5e7eb;
    }

    .country-code__trigger:hover {
      background-color: #f9fafb;
    }

    .country-code__flag-img {
      width: 20px;
      height: 15px;
      object-fit: cover;
      border-radius: 2px;
    }

    .country-code__value {
      font-weight: 500;
      color: #374151;
      font-size: 0.875rem;
    }

    .country-code__arrow {
      font-size: 0.75rem;
      color: #6b7280;
      margin-left: auto;
    }

    .country-code__list {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #d1d5db;
      border-radius: 0.5rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      z-index: 1002;
      max-height: 200px;
      overflow-y: auto;
      min-width: 250px;
      margin-top: 2px;
    }

    .country-code__item {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      cursor: pointer;
      gap: 0.75rem;
      border-bottom: 1px solid #f3f4f6;
    }

    .country-code__item:hover {
      background-color: #f9fafb;
    }

    .country-code__item--active {
      background-color: #eff6ff;
      color: #1d4ed8;
    }

    .country-flag-img {
      width: 20px;
      height: 15px;
      object-fit: cover;
      border-radius: 2px;
    }

    .country-dial {
      font-weight: 500;
      color: #374151;
      font-size: 0.875rem;
      min-width: 60px;
    }

    .country-name {
      color: #6b7280;
      font-size: 0.875rem;
      flex: 1;
    }

    .phone-input__field {
      flex: 1;
      padding: 0.75rem;
      border: none;
      outline: none;
      font-size: 0.875rem;
      background: transparent;
    }
  `],
  standalone: false
})
export class StandalonePhoneTestComponent {
  isDropdownOpen = false;
  countries: Country[] = [];
  selectedCountry: Country;
  phoneNumber = '';

  constructor(private countryService: CountryService) {
    this.countries = this.countryService.getCountries();
    this.selectedCountry = this.countryService.getDefaultCountry();
    console.log('StandalonePhoneTest initialized with countries:', this.countries.length);
  }

  toggleDropdown(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    this.isDropdownOpen = !this.isDropdownOpen;
    console.log('Dropdown toggled:', this.isDropdownOpen);
  }

  selectCountry(country: Country, event: Event) {
    event.preventDefault();
    event.stopPropagation();
    this.selectedCountry = country;
    this.isDropdownOpen = false;
    console.log('Country selected:', country.name);
  }
}
