import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { SchoolEnrollment, CreateSchoolEnrollment } from '../models/school-enrollment.models';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class SchoolEnrollmentService {
  private readonly API_URL = environment.apiBaseUrl;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Get all school enrollments (for admin dashboard)
   */
  getAllEnrollments(): Observable<SchoolEnrollment[]> {
    const headers = this.getAuthHeaders();
    
    return this.http.get<SchoolEnrollment[]>(
      `${this.API_URL}/api/school-enrollments/`,
      { headers }
    ).pipe(
      tap(enrollments => {
        console.log('Fetched school enrollments:', enrollments);
      }),
      catchError(error => {
        console.error('Error fetching school enrollments:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new school enrollment (public endpoint for main page)
   */
  createEnrollment(enrollment: CreateSchoolEnrollment): Observable<SchoolEnrollment> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<SchoolEnrollment>(
      `${this.API_URL}/api/school-enrollments/`,
      enrollment,
      { headers }
    ).pipe(
      tap(response => {
        console.log('School enrollment created:', response);
      }),
      catchError(error => {
        console.error('Error creating school enrollment:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Update a school enrollment (for admin use)
   */
  updateEnrollment(id: number, enrollment: Partial<SchoolEnrollment>): Observable<SchoolEnrollment> {
    const headers = this.getAuthHeaders();

    return this.http.patch<SchoolEnrollment>(
      `${this.API_URL}/api/school-enrollments/${id}/`,
      enrollment,
      { headers }
    ).pipe(
      tap(response => {
        console.log('School enrollment updated:', response);
      }),
      catchError(error => {
        console.error('Error updating school enrollment:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a school enrollment (for admin use)
   */
  deleteEnrollment(id: number): Observable<void> {
    const headers = this.getAuthHeaders();

    return this.http.delete<void>(
      `${this.API_URL}/api/school-enrollments/${id}/`,
      { headers }
    ).pipe(
      tap(() => {
        console.log('School enrollment deleted:', id);
      }),
      catchError(error => {
        console.error('Error deleting school enrollment:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get authorization headers for authenticated requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAccessToken();
    
    if (!token) {
      throw new Error('No access token available');
    }

    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  }
}
