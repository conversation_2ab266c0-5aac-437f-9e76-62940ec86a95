import { FormControl } from '@angular/forms';
import { phoneValidator, russianPhoneValidator, formatPhoneInput, getPhoneErrorMessage, getRussianPhoneErrorMessage } from './phone.validator';

describe('PhoneValidator', () => {
  let validator: any;

  beforeEach(() => {
    validator = phoneValidator();
  });

  describe('phoneValidator', () => {
    it('should return null for empty value', () => {
      const control = new FormControl('');
      const result = validator(control);
      expect(result).toBeNull();
    });

    it('should return null for null value', () => {
      const control = new FormControl(null);
      const result = validator(control);
      expect(result).toBeNull();
    });

    it('should return null for valid +7 phone number', () => {
      const control = new FormControl('+79123456789');
      const result = validator(control);
      expect(result).toBeNull();
    });

    it('should return null for valid 8 phone number', () => {
      const control = new FormControl('89123456789');
      const result = validator(control);
      expect(result).toBeNull();
    });

    it('should return error for phone starting with 7 without +', () => {
      const control = new FormControl('79123456789');
      const result = validator(control);
      expect(result).toEqual({ invalidPhone: true });
    });

    it('should return error for phone with less than 11 digits', () => {
      const control = new FormControl('+7912345678');
      const result = validator(control);
      expect(result).toEqual({ invalidPhone: true });
    });

    it('should return error for phone with more than 11 digits', () => {
      const control = new FormControl('+791234567890');
      const result = validator(control);
      expect(result).toEqual({ invalidPhone: true });
    });

    it('should return error for phone starting with other digits', () => {
      const control = new FormControl('99123456789');
      const result = validator(control);
      expect(result).toEqual({ invalidPhone: true });
    });

    it('should handle phone with spaces and dashes', () => {
      const control = new FormControl('****** 345-67-89');
      const result = validator(control);
      expect(result).toBeNull();
    });
  });

  describe('formatPhoneInput', () => {
    it('should format phone starting with 7 to +7', () => {
      const mockEvent = {
        target: { value: '79123456789' }
      };
      const result = formatPhoneInput(mockEvent);
      expect(result).toBe('+79123456789');
    });

    it('should keep +7 format unchanged', () => {
      const mockEvent = {
        target: { value: '+79123456789' }
      };
      const result = formatPhoneInput(mockEvent);
      expect(result).toBe('+79123456789');
    });

    it('should keep 8 format unchanged', () => {
      const mockEvent = {
        target: { value: '89123456789' }
      };
      const result = formatPhoneInput(mockEvent);
      expect(result).toBe('89123456789');
    });

    it('should limit +7 format to 12 characters', () => {
      const mockEvent = {
        target: { value: '+791234567890123' }
      };
      const result = formatPhoneInput(mockEvent);
      expect(result).toBe('+79123456789');
    });

    it('should limit 8 format to 11 characters', () => {
      const mockEvent = {
        target: { value: '891234567890123' }
      };
      const result = formatPhoneInput(mockEvent);
      expect(result).toBe('89123456789');
    });

    it('should remove non-digit characters except +', () => {
      const mockEvent = {
        target: { value: '+7 (912) 345-67-89' }
      };
      const result = formatPhoneInput(mockEvent);
      expect(result).toBe('+79123456789');
    });
  });

  describe('getPhoneErrorMessage', () => {
    it('should return required message for required error', () => {
      const errors = { required: true };
      const result = getPhoneErrorMessage(errors);
      expect(result).toBe('Это поле обязательно для заполнения');
    });

    it('should return phone format message for invalidPhone error', () => {
      const errors = { invalidPhone: true };
      const result = getPhoneErrorMessage(errors);
      expect(result).toBe('Номер должен начинаться с +7 или 8 и содержать 11 цифр');
    });

    it('should return pattern message for pattern error', () => {
      const errors = { pattern: true };
      const result = getPhoneErrorMessage(errors);
      expect(result).toBe('Введите корректный номер телефона');
    });

    it('should return empty string for no errors', () => {
      const result = getPhoneErrorMessage(null);
      expect(result).toBe('');
    });

    it('should return empty string for unknown errors', () => {
      const errors = { unknownError: true };
      const result = getPhoneErrorMessage(errors);
      expect(result).toBe('');
    });
  });
});
