<header class="l-header">
    <nav class="nav bd-grid">
        <div>
            <a href="#" class="nav-logo">Easy Prod</a>
        </div>

        <div class="nav-menu" id="nav-menu">
            <ul class="nav-list">
                <li class="nav-item"><a href="#home" class="nav-link active">Главное</a></li> 
                <li class="nav-item"><a href="#about" class="nav-link">О нас</a></li>           
                <li class="nav-item"><a href="#portfolio" class="nav-link">Наши проекты</a></li>
                <li class="nav-item"><a href="#contact" class="nav-link">Контакты</a></li>
                <li class="nav-item"><a routerLink="/" class="nav-link ">Наша Школа</a></li>
            </ul>
        </div>

        <div class="nav-toggle" id="nav-toggle">
            <i class='bx bx-menu'></i>
        </div>
    </nav>
</header>

<main class="l-main">
    <section class="home" id="home">
        <div class="home-container bd-grid">
            <h1 class="home-title"><span>Создаем сайты</span><br>и приложения<br>
                <a href="#contact" class="button-main">Связаться</a>
            </h1>
            
            <div class="home-scroll">
                <a href="#about" class="home-scroll-link"><i class='bx bx-up-arrow-alt' ></i>О нас</a>
            </div>

            <div class="ai-bot home-img" >
                <div class="head">
                  <div class="face">
                    <div class="eyes"> </div>
                    <div class="mouth"> </div>
                  </div>
                </div>
            </div>
        </div>
    </section>

    <section class="about section" id="about">
        <h2 class="section-title">О нас</h2>

        <div class="about-container bd-grid">
            <div class="about-img">
                <img src="assets/studio/img/1.png" alt="Easy Prod image">
            </div>

            <div>
                <h2 class="about-subtitle">Easy Prod</h2>
                <span class="about-profession">Мы — независимая студия цифрового продакшна. Создаём сайты, мобильные приложения, чат-боты, веб-сервисы, интерфейсы, кастомные CRM, трекеры и любые digital-решения, которые помогают бизнесу, людям и проектам быть в онлайне уверенно и удобно.</span>
                <p class="about-text">У нас за плечами — коммерческие платформы, стартапы, интерактивные игры, сервисы для мероприятий и нестандартные приложения. Мы не боимся сложных задач — наоборот, любим находить оригинальные и технологичные решения там, где шаблоны не работают.</p>
                <p class="about-text">Наша миссия — превращать идеи в реальные цифровые продукты, которые живут, работают и приносят результат</p>
            </div>   
        </div>
    </section>

    <section class="portfolio section" id="portfolio">
        <h2 class="section-title">Наши проекты</h2>
        <p style="text-align: center; margin-bottom: 40px;"><br>Мы также разрабатываем мобильные приложения, чат-боты и другие цифровые решения<br>расскажем о них при личной встрече или по запросу</p>

        <div class="portfolio-container bd-grid">
            <div class="portfolio-img">
                <img src="assets/studio/img/pro1.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Интаго КЗ</h1>
                <p>Производственная компания</p>
                <div class="portfolio-link">
                    <a href="https://www.intagokazakhstan.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro4.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Del Force</h1>
                <p>Магазин Строй товаров</p>
                <div class="portfolio-link">
                    <a href="https://www.delforce.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro3.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Global TS</h1>
                <p>Строительная компания</p>

                <div class="portfolio-link">
                    <a href="https://globalserv.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro2.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт USS 1</h1>
                <p>Строительная компания</p>

                <div class="portfolio-link">
                    <a href="https://www.uss1.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro5.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Корпоративный LMS</h1>
                <p>Для управления образовательным процессом</p>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro6.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Exclusive Moving</h1>
                <p>Грузовая компания USA Los-Angeles</p>

                <div class="portfolio-link">
                    <a href="https://exclusivemovingla.com/" class="portfolio-link-name"  target="_blank">Посмотреть</a>
                </div>
            </div>

        </div>
    </section>
    
    <div id="sphe-app">
      <div class="sphe-hero">
        <div class="sphe-hero-text-background">
          <h1 class="sphe-title-1 text-center">Создаем лёгкость</h1>
          <h2 class="sphe-title-2 text-center">в цифровом мире</h2>
        </div>
      </div>

      <canvas #spheCanvas id="sphe-webgl-canvas"></canvas>
    </div>
    
    <section class="contact section" id="contact">
        <div class="contact-hero">
            <h2 class="section-title">Готовы воплотить вашу идею?</h2>
            <p class="contact-subtitle-text">Расскажите о вашем проекте, и мы создадим что-то удивительное вместе</p>
        </div>

        <div class="contact-container bd-grid">
            <!-- Contact Info Cards -->
            <div class="contact-info">
                <div class="contact-card">
                    <div class="contact-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C19.5304 19 20.0391 18.7893 20.4142 18.4142C20.7893 18.0391 21 17.5304 21 17V7C21 6.46957 20.7893 5.96086 20.4142 5.58579C20.0391 5.21071 19.5304 5 19 5H5C4.46957 5 3.96086 5.21071 3.58579 5.58579C3.21071 5.96086 3 6.46957 3 7V17C3 17.5304 3.21071 18.0391 3.58579 18.4142C3.96086 18.7893 4.46957 19 5 19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="contact-card-content">
                        <h3 class="contact-card-title">Email</h3>
                        <span class="contact-card-text">kopyrin7&#64;gmail.com</span>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="contact-card-content">
                        <h3 class="contact-card-title">Телефон</h3>
                        <span class="contact-card-text">+7 705 308 55 57</span>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <img src="assets/img/logos/whatsapp-icon.svg" alt="WhatsApp" width="24" height="24">
                    </div>
                    <div class="contact-card-content">
                        <h3 class="contact-card-title">WhatsApp</h3>
                        <span class="contact-card-text">+7 705 308 55 57</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Contact Form -->
            <div class="contact-form-wrapper">
                <form class="contact-form" [formGroup]="contactForm" (ngSubmit)="onContactSubmit()">
                    <!-- Success Message -->
                    <div *ngIf="submitMessage" class="alert alert-success">
                        <div class="alert-icon">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill="currentColor"/>
                            </svg>
                        </div>
                        <span>{{ submitMessage }}</span>
                    </div>

                    <!-- Error Message -->
                    <div *ngIf="submitError" class="alert alert-error">
                        <div class="alert-icon">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                            </svg>
                        </div>
                        <span>{{ submitError }}</span>
                    </div>

                    <div class="form-grid">
                        <div class="input-group">
                            <label class="input-label">Ваше имя</label>
                            <div class="input-wrapper">
                                <input
                                    type="text"
                                    placeholder="Введите ваше имя"
                                    class=" contact-input"
                                    [class.error]="isFieldInvalid('name')"
                                    formControlName="name"
                                >
                                <div class="input-icon">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10 9C11.6569 9 13 7.65685 13 6C13 4.34315 11.6569 3 10 3C8.34315 3 7 4.34315 7 6C7 7.65685 8.34315 9 10 9Z" fill="currentColor"/>
                                        <path d="M3 18C3 14.134 6.13401 11 10 11C13.866 11 17 14.134 17 18H3Z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div *ngIf="isFieldInvalid('name')" class="error-message">
                                {{ getFieldError('name') }}
                            </div>
                        </div>

                        <div class="input-group"> 
                            <label class="input-label">Номер</label>
                            <app-phone-input
                                formControlName="phone"
                                [showError]="isFieldInvalid('phone')"
                                [errorMessage]="getFieldError('phone')"
                                [required]="true"
                                [showLabel]="false">
                            </app-phone-input>
                        </div>
                    </div>

                    <div class="input-group">
                        <label class="input-label">Расскажите о вашем проекте</label>
                        <div class="input-wrapper">
                            <textarea
                                rows="6"
                                class="contact-input contact-textarea"
                                placeholder="Опишите ваш проект, цели, требования и пожелания..."
                                [class.error]="isFieldInvalid('message')"
                                formControlName="message"
                            ></textarea>
                            <div class="input-icon textarea-icon">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2.5 3.75C2.5 3.41848 2.6317 3.10054 2.86612 2.86612C3.10054 2.6317 3.41848 2.5 3.75 2.5H16.25C16.5815 2.5 16.8995 2.6317 17.1339 2.86612C17.3683 3.10054 17.5 3.41848 17.5 3.75V14.375C17.5 14.7065 17.3683 15.0245 17.1339 15.2589C16.8995 15.4933 16.5815 15.625 16.25 15.625H5.625L2.5 18.75V3.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        </div>
                        <div *ngIf="isFieldInvalid('message')" class="error-message">
                            {{ getFieldError('message') }}
                        </div>
                    </div>

                    <button
                        type="submit"
                        class="contact-button"
                        [disabled]="isSubmitting"
                        [class.loading]="isSubmitting"
                    >
                        <span *ngIf="isSubmitting" class="spinner"></span>
                        <span class="button-text">{{ isSubmitting ? 'Отправляем...' : 'Отправить заявку' }}</span>
                        <div class="button-icon" *ngIf="!isSubmitting">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.5 10L2.5 10M17.5 10L11.25 3.75M17.5 10L11.25 16.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </button>
                </form>
            </div>
        </div>
    </section>

</main>

<footer class="footer section">
    <div class="footer-container bd-grid">
        <div class="footer-data">
            <h2 class="footer-title">Easy Prod</h2>
            <ul>
                <li><a href="#home" class="footer-link">Главная</a></li>
                <li><a href="#about" class="footer-link">О нас</a></li>                       
                <li><a href="#portfolio" class="footer-link">Наши проекты</a></li>
                <li><a href="#contact" class="footer-link">Контакты</a></li>
            </ul>
        </div>
    </div>
</footer>

<!-- Contact Popup -->
<app-contact-popup></app-contact-popup>
