import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { AuthService } from '../../../../core/services/auth.service';
import { StudioRequestsService } from '../../../../core/services/studio-requests.service';
import { SchoolEnrollmentService } from '../../../../core/services/school-enrollment.service';
import { User } from '../../../../core/models/auth.models';
import { StudioRequest, STUDIO_REQUEST_STATUS_LABELS, STUDIO_REQUEST_STATUS_COLORS } from '../../../../core/models/studio-request.models';
import { SchoolEnrollment } from '../../../../core/models/school-enrollment.models';

@Component({
  selector: 'app-dashboard',
  standalone: false,
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  studioRequests: StudioRequest[] = [];
  schoolEnrollments: SchoolEnrollment[] = [];
  isLoadingRequests = false;
  isLoadingEnrollments = false;
  requestsError = '';
  enrollmentsError = '';
  private destroy$ = new Subject<void>();

  // Expose constants to template
  readonly statusLabels = STUDIO_REQUEST_STATUS_LABELS;
  readonly statusColors = STUDIO_REQUEST_STATUS_COLORS;

  constructor(
    private authService: AuthService,
    private studioRequestsService: StudioRequestsService,
    private schoolEnrollmentService: SchoolEnrollmentService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        if (!authState.isAuthenticated) {
          this.router.navigate(['/login']);
          return;
        }
        this.currentUser = authState.user;
        this.loadStudioRequests();
        this.loadSchoolEnrollments();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onLogout(): void {
    this.authService.logout();
  }

  navigateToStudio(): void {
    this.router.navigate(['/studio']);
  }

  navigateToMain(): void {
    this.router.navigate(['/']);
  }

  loadStudioRequests(): void {
    this.isLoadingRequests = true;
    this.requestsError = '';

    this.studioRequestsService.getAllRequests()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (requests) => {
          this.studioRequests = requests;
          this.isLoadingRequests = false;
        },
        error: (error) => {
          this.isLoadingRequests = false;
          this.handleRequestsError(error);
        }
      });
  }

  private handleRequestsError(error: any): void {
    if (error.status === 401) {
      this.requestsError = 'Ошибка авторизации. Попробуйте войти заново.';
      this.authService.logout();
    } else if (error.status === 403) {
      this.requestsError = 'Недостаточно прав для просмотра заявок.';
    } else if (error.status === 0) {
      this.requestsError = 'Ошибка подключения к серверу.';
    } else {
      this.requestsError = 'Ошибка при загрузке заявок.';
    }
  }

  refreshRequests(): void {
    this.loadStudioRequests();
  }

  loadSchoolEnrollments(): void {
    this.isLoadingEnrollments = true;
    this.enrollmentsError = '';

    this.schoolEnrollmentService.getAllEnrollments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (enrollments) => {
          this.schoolEnrollments = enrollments;
          this.isLoadingEnrollments = false;
        },
        error: (error) => {
          this.isLoadingEnrollments = false;
          this.handleEnrollmentsError(error);
        }
      });
  }

  private handleEnrollmentsError(error: any): void {
    if (error.status === 401) {
      this.enrollmentsError = 'Ошибка авторизации. Попробуйте войти заново.';
      this.authService.logout();
    } else if (error.status === 403) {
      this.enrollmentsError = 'Недостаточно прав для просмотра записей.';
    } else if (error.status === 0) {
      this.enrollmentsError = 'Ошибка подключения к серверу.';
    } else {
      this.enrollmentsError = 'Ошибка при загрузке записей.';
    }
  }

  refreshEnrollments(): void {
    this.loadSchoolEnrollments();
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  updateRequestStatus(request: StudioRequest, newStatus: 'new' | 'in_progress' | 'done'): void {
    this.studioRequestsService.updateRequest(request.id, { status: newStatus })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedRequest) => {
          const index = this.studioRequests.findIndex(r => r.id === request.id);
          if (index !== -1) {
            this.studioRequests[index] = updatedRequest;
          }
        },
        error: (error) => {
          console.error('Error updating request status:', error);
        }
      });
  }
}
