@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

/* Apply Montserrat font globally for this component */
:host {
  font-family: 'Montserrat', sans-serif;
}

.main-title{
  font-family: 'Montserrat', sans-serif;
  font-weight: 900;
}

.main-line-height{
  line-height:50px;
}

@media (max-width: 640px) {
  .main-line-height{
    line-height:33px;
  }
}

html {
  scroll-behavior: smooth;
}

/* Ensure smooth scrolling for all anchor links */
* {
  scroll-behavior: smooth;
}

/* Accordion animations */
.accordion-item {
  border-bottom: 1px solid #e5e7eb;
}

.accordion-content {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.accordion-content.hidden {
  max-height: 0;
  opacity: 0;
}

.accordion-content:not(.hidden) {
  max-height: 500px;
  opacity: 1;
}

/* Smooth icon rotation */
.accordion-item svg {
  transition: transform 0.2s ease-in-out;
}

/* Hover effects for accordion buttons */
.accordion-item button:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
