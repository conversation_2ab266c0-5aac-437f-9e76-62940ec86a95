<nav class="bg-white fixed w-full z-20 top-0 start-0 border-b border-gray-200">
  <div
    class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-1 py-4"
  >
    <a href="/" class="flex items-center space-x-3 rtl:space-x-reverse">
      <span class="self-center lg:text-4xl text-4xl text-purple-900 main-title"
        >ITeasy School</span
      >
    </a>
    <div class="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
      <a
        href="#form"
        class="text-white bg-purple-800 hover:scale-95 lg:block hidden transition-all focus:ring-4 focus:outline-none text-sm focus:ring-blue-300 font-medium rounded-lg px-3 py-2 text-center"
      >
        Записаться на бесплатный урок
      </a>
      <button
        data-collapse-toggle="navbar-sticky"
        type="button"
        class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-800 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
        aria-controls="navbar-sticky"
        aria-expanded="false"
      >
        <span class="sr-only">Open main menu</span>
        <svg
          class="w-5 h-5"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 17 14"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M1 1h15M1 7h15M1 13h15"
          />
        </svg>
      </button>
    </div>
    <div
      class="items-center justify-between hidden w-full md:flex md:w-auto md:order-1"
      id="navbar-sticky"
    >
      <ul
        class="flex flex-col p-4 md:p-0 mt-4 font-medium border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white"
      >
        <li>
          <a
            href="#review"
            class="block py-2 px-3 text-gray-900 rounded md:bg-transparent md:p-0"
            aria-current="page"
            >Отзывы</a
          >
        </li>
        <li>
          <a
            href="#courses"
            class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0"
          >
            Курсы
          </a>
        </li>
        <li>
          <a
            href="#faq"
            class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0"
          >
            FAQ
          </a>
        </li>
        <li>
          <a
            href="/studio"
            class="block py-2 px-3 text-blue-700 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-800 md:p-0"
          >
            Студия разработки
          </a>
        </li>
        <li class="sm:hidden block">
          <a
            href="#form"
            class="sm:hidden block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0"
          >
            Записаться на пробный урок
          </a>
        </li>
      </ul>
    </div>
  </div>
</nav>
<main>
  <div
    class="w-full pt-36"
    style="
      background-color: #21d4fd;
      background-image: linear-gradient(19deg, #21d4fd 0%, #b721ff 100%);
    "
  >
    <div class="max-w-screen-xl mx-auto p-1 pb-20">
      <div class="grid xl:grid-cols-2 gap-1 gap-y-8">
        <div class="text-white">
          <div class="uppercase text-sm font-medium drop-shadow-md">
            Обучайся и развивайся с нами в IT!
          </div>
          <h1
            class="font-bold lg:text-5xl main-line-height text-2xl mt-5 drop-shadow-2xl tracking-normal"
          >
            Профориентационные <span class="text-purple-800"> курсы </span> по
            программированию и компьютерной графике для детей
          </h1>
          <div class="flex flex-wrap items-center gap-x-5 gap-y-3 mt-10">
            <a
              href="#form"
              class="border-2 border-white text-white lg:py-3 lg:px-10 py-1 px-3 lg:text-base text-sm rounded-xl font-medium hover:scale-105 shadow-xl transition-all"
            >
              Записаться на пробный урок
            </a>
            <a href="#courses" class="font-medium underline">
              Посмотреть курсы
            </a>
          </div>
        </div>
        <div class="text-white">
          <!--          <img src="assets/img/book-lovers.svg" alt="">-->
          <!--          <div class="transform scale-x-[-1] w-[550px]">-->
          <div class="">
            <ng-lottie [options]="options"></ng-lottie>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-full"
      style="
        background-color: #21d4fd;
        background-image: linear-gradient(160deg, #21d4fd 0%, #b721ff 100%);
      "
    >
      <div class="max-w-screen-xl mx-auto p-1">
        <div class="grid lg:grid-cols-6 grid-cols-3 gap-2">
          <div class="h-32 flex justify-center items-center gap-3">
            <div class="font-bold text-2xl text-white">Html</div>
            <img src="assets/img/logos/htmk.svg" class="w-8" alt="" />
          </div>

          <div class="h-32 flex justify-center items-center gap-3">
            <div class="font-bold text-2xl text-white">JavaScript</div>
            <img src="assets/img/logos/javascript.svg" class="w-8" alt="" />
          </div>

          <div class="h-32 flex justify-center items-center gap-3">
            <div class="font-bold text-2xl text-white">Php</div>
            <img src="assets/img/logos/php.svg" class="w-10" alt="" />
          </div>

          <div class="h-32 flex justify-center items-center gap-3">
            <div class="font-bold text-2xl text-white">Blender</div>
            <img src="assets/img/logos/blender.svg" class="w-8" alt="" />
          </div>

          <div class="h-32 flex justify-center items-center gap-3">
            <div class="font-bold text-2xl text-white">Unity</div>
            <img src="assets/img/logos/unity.svg" class="w-8" alt="" />
          </div>

          <div class="h-32 flex justify-center items-center gap-3">
            <div class="font-bold text-2xl text-white">Python</div>
            <img src="assets/img/logos/python.svg" class="w-8" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="w-full mb-32">
    <div class="max-w-screen-xl mx-auto p-1">
      <div id="courses"></div>
      <h3 class="text-center font-bold text-2xl mb-4 mt-32">Наши курсы</h3>
      <div
        class="grid lg:grid-cols-3 content-center md:grid-cols-2 gap-x-10 gap-y-5"
      >
        <div
          class="shadow-md rounded-xl hover:scale-105 duration-500 ease-in-out"
        >
          <div
            class="bg-gray-50 h-52 rounded-t-xl flex items-center justify-center"
          >
            <img src="assets/img/html.png" class="w-60" alt="" />
          </div>
          <div class="p-5">
            <div class="h-40">
              <div class="font-medium text-xl">Программирование</div>
              <div class="mt-2 text-sm">
                <span class="font-medium"> 1 курс:</span>
                разработка сайтов (frontend) создание 2D игр (Javascript)
              </div>
              <div class="mt-1 text-sm">
                <span class="font-medium"> 2 курс:</span>
                разработка сервисов (backend) создание 3D игр (Unity, C#)
              </div>
              <div class="mt-2 text-sm">
                <span class="font-medium">Длительность: </span> 18 месяцев
              </div>
            </div>
            <div class="flex mt-1 mb-2 justify-between">
              <div class="text-xl font-medium">30.000 ₸</div>
              <a
                href="../../../assets/files/coding.pdf"
                download="coding.pdf"
                class="bg-purple-600 shadow-md text-sm text-center hover:bg-purple-500 duration-300 rounded-md px-2 py-1 text-white flex items-center justify-center gap-2"
              >
                Скачать программу
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-file-earmark-arrow-down"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8.5 6.5a.5.5 0 0 0-1 0v3.793L6.354 9.146a.5.5 0 1 0-.708.708l2 2a.5.5 0 0 0 .708 0l2-2a.5.5 0 0 0-.708-.708L8.5 10.293z"
                  />
                  <path
                    d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="shadow-md rounded-xl hover:scale-105 duration-500 ease-in-out"
        >
          <div
            class="bg-gray-50 h-52 rounded-t-xl flex items-center justify-center"
          >
            <img src="assets/img/blender.png" class="w-60" alt="" />
          </div>
          <div class="p-5">
            <div class="h-40">
              <div class="font-medium text-xl">Компьютерная графика</div>
              <div class="mt-2 text-sm">
                <span class="font-medium"> 1 курс:</span>
                создание 3D моделей и сцен анимация роликов
              </div>
              <div class="mt-1 text-sm">
                <span class="font-medium"> 2 курс:</span>
                анимация персонажей дизайн сайтов и приложений монтаж и
                видеоэффекты
              </div>
              <div class="mt-2 text-sm">
                <span class="font-medium">Длительность: </span> 18 месяцев
              </div>
            </div>
            <div class="flex mt-1 mb-2 justify-between">
              <div class="text-xl font-medium">30.000 ₸</div>
              <a
                href="../../../assets/files/graphic.pdf"
                download="graphic.pdf"
                class="bg-purple-600 shadow-md text-sm text-center hover:bg-purple-500 duration-300 rounded-md px-2 py-1 text-white flex items-center justify-center gap-2"
              >
                Скачать программу
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-file-earmark-arrow-down"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8.5 6.5a.5.5 0 0 0-1 0v3.793L6.354 9.146a.5.5 0 1 0-.708.708l2 2a.5.5 0 0 0 .708 0l2-2a.5.5 0 0 0-.708-.708L8.5 10.293z"
                  />
                  <path
                    d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="shadow-md rounded-xl hover:scale-105 duration-500 ease-in-out"
        >
          <div
            class="bg-gray-50 h-52 rounded-t-xl flex items-center justify-center"
          >
            <img src="assets/img/python%20logo.png" class="w-60" alt="" />
          </div>
          <div class="p-5">
            <div class="h-40">
              <div class="font-medium text-xl">Основы Python</div>
              <div class="mt-2 text-sm">
                Курс посвящен базовым понятиям и элементам языка
                программирования
              </div>
              <div class="mt-1 text-sm">
                Для ребят без опыта, которые мечтают написать свой первый код
              </div>
              <div class="mt-2 text-sm">
                <span class="font-medium">Длительность: </span> 3 месяца
              </div>
            </div>
            <div class="flex mt-1 mb-2 justify-between">
              <div class="text-xl font-medium">25.000 ₸</div>
              <button
                class="bg-purple-600 shadow-md text-sm text-center hover:bg-purple-500 duration-300 rounded-md px-2 py-1 text-white flex items-center justify-center gap-2"
              >
                Скачать программу
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-file-earmark-arrow-down"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8.5 6.5a.5.5 0 0 0-1 0v3.793L6.354 9.146a.5.5 0 1 0-.708.708l2 2a.5.5 0 0 0 .708 0l2-2a.5.5 0 0 0-.708-.708L8.5 10.293z"
                  />
                  <path
                    d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="w-full mt-32 mb-32">
    <div class="max-w-screen-xl mx-auto p-1">
      <h3 class="text-center font-bold text-2xl mb-12">
        Как проходит обучение в ITeasy?
      </h3>
      <div class="grid md:grid-cols-2 gap-x-14 gap-y-10 text-xl font-medium">
        <div class="lg:px-24 px-2 flex gap-x-2 items-center">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="25"
              fill="currentColor"
              class="bi bi-check-circle"
              viewBox="0 0 16 16"
            >
              <path
                d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
              />
              <path
                d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05"
              />
            </svg>
          </div>
          <div class="leading-5">2 учебных года по одному направлению</div>
        </div>
        <div class="lg:px-24 px-2 flex gap-x-2 items-center">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="25"
              fill="currentColor"
              class="bi bi-check-circle"
              viewBox="0 0 16 16"
            >
              <path
                d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
              />
              <path
                d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05"
              />
            </svg>
          </div>
          <div class="leading-5">Группа из 8-9 человек</div>
        </div>
        <div class="lg:px-24 px-2 flex gap-x-2 items-center">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="25"
              fill="currentColor"
              class="bi bi-check-circle"
              viewBox="0 0 16 16"
            >
              <path
                d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
              />
              <path
                d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05"
              />
            </svg>
          </div>
          <div class="leading-5">3 дня в неделю по часу</div>
        </div>
        <div class="lg:px-24 px-2 flex gap-x-2 items-center">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="25"
              fill="currentColor"
              class="bi bi-check-circle"
              viewBox="0 0 16 16"
            >
              <path
                d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
              />
              <path
                d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05"
              />
            </svg>
          </div>
          <div class="leading-5">
            Очно в классе, но есть возможность записаться на онлайн
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    class="w-full mt-32"
    style="
      background-color: #21d4fd;
      background-image: linear-gradient(210deg, #21d4fd 0%, #b721ff 100%);
    "
  >
    <div class="pt-20">
      <div class="max-w-screen-xl mx-auto p-1 text-white">
        <h3 class="text-center font-bold text-4xl">
          Мы <span class="text-cyan-200">лучший </span> выбор потому что мы..
        </h3>
        <h3 class="text-center font-bold text-2xl mb-4 mt-4 underline">
          Вводим в индустрию
        </h3>
        <div class="grid xl:grid-cols-3 lg:grid-cols-2 gap-10 mt-20 text-xl">
          <div class="h-44 text-center">
            <div class="flex justify-center items-center">
              <img src="assets/img/decision-making.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Ученики пробуют себя примерно в 10 разных специальностях
            </div>
          </div>
          <div class="h-44 text-center border-l border-r">
            <div class="flex justify-center items-center">
              <img src="assets/img/team.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Помимо практики, ребята узнают чем живёт и дышит сообщество
            </div>
          </div>
          <div class="h-44 text-center">
            <div class="flex justify-center items-center">
              <img src="assets/img/struggle.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Прикладные знания и проекты. Ничего не делаем "в стол"
            </div>
          </div>
        </div>
      </div>
      <div class="pt-14 pb-4">
        <div class="grid xl:grid-cols-4 lg:grid-cols-2 gap-x-1">
          <div class="h-72" *ngFor="let img of images; let i = index">
            <img
              [src]="img"
              class="w-full h-full object-cover hover:scale-95 transition-all rounded-xl cursor-pointer"
              (click)="openModal(img)"
              alt="Image {{ i + 1 }}"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="pt-16">
      <div class="max-w-screen-xl mx-auto p-1 text-white">
        <h3 class="text-center font-bold text-2xl mb-4 mt-4 underline">
          Раскрываем таланты
        </h3>
        <div class="grid xl:grid-cols-3 lg:grid-cols-2 gap-10 mt-20 text-xl">
          <div class="h-44 text-center">
            <div class="flex justify-center items-center">
              <img src="assets/img/learning.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Увлечём любого. Потому что каждый из 218 уроков построен на
              интересах и поп-культуре
            </div>
          </div>
          <div class="h-44 text-center border-l border-r">
            <div class="flex justify-center items-center">
              <img src="assets/img/growth.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Наша программа лёгкая и пошаговая. Любой ребёнок сможет научиться
              и затем творить
            </div>
          </div>
          <div class="h-44 text-center">
            <div class="flex justify-center items-center">
              <img src="assets/img/puzzle.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Мы проходим так много разных и интересных тем, что вероятность "не
              найти себя" очень мала
            </div>
          </div>
        </div>
      </div>
      <div class="pt-14 pb-4">
        <div class="grid xl:grid-cols-4 lg:grid-cols-2 gap-x-1">
          <div class="h-72" *ngFor="let img of images2; let i = index">
            <img
              [src]="img"
              class="w-full h-full object-cover hover:scale-95 transition-all rounded-xl cursor-pointer"
              (click)="openModal(img)"
              alt="Image {{ i + 1 }}"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="pt-16">
      <div class="max-w-screen-xl mx-auto p-1 text-white">
        <h3 class="text-center font-bold text-2xl mb-4 mt-4 underline">
          Развиваем дополнительно
        </h3>
        <div class="grid xl:grid-cols-3 lg:grid-cols-2 gap-10 mt-20 text-xl">
          <div class="h-44 text-center">
            <div class="flex justify-center items-center">
              <img src="assets/img/3question.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Прививаем самообучение. Учим искать и обрабатывать информацию
            </div>
          </div>
          <div class="h-44 text-center border-l border-r">
            <div class="flex justify-center items-center">
              <img src="assets/img/team%20(1).png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Ученики создают проекты в командах, учатся совместному творчеству
            </div>
          </div>
          <div class="h-44 text-center">
            <div class="flex justify-center items-center">
              <img src="assets/img/puzzle.png" class="w-20" alt="" />
            </div>
            <div class="font-bold px-10">
              Не заставляем, а создаём среду. И ученики сами участвуют в
              конкурсах и хакатонах
            </div>
          </div>
        </div>
      </div>
      <div class="pt-14 pb-4">
        <div class="grid xl:grid-cols-4 lg:grid-cols-2 gap-x-1">
          <div class="h-72" *ngFor="let img of images3; let i = index">
            <img
              [src]="img"
              class="w-full h-full object-cover hover:scale-95 transition-all rounded-xl cursor-pointer"
              (click)="openModal(img)"
              alt="Image {{ i + 1 }}"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    [@fadeInOut]
    *ngIf="isModalOpen"
    (click)="closeModal()"
    class="fixed inset-0 bg-black bg-opacity-80 z-50 flex justify-center items-center p-10"
  >
    <div class="w-10/12">
      <div
        (click)="closeModal()"
        class="cursor-pointer text-sm text-gray-100 underline text-right"
      >
        Закрыть
      </div>
      <div class="flex items-center justify-center">
        <img
          [src]="selectedImage"
          class="w-full h-full object-contain rounded-md"
          alt="Fullscreen Image"
          (click)="closeModal()"
        />
      </div>
    </div>
  </div>

  <div class="w-full mb-32">
    <div class="max-w-screen-xl mx-auto p-1">
      <div id="review"></div>
      <h3 class="text-center font-bold text-2xl mt-32 mb-12">
        Интервью о начале обучения в ITeasy
      </h3>
      <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-x-10 gap-y-5">
        <div class="shadow-md rounded-xl">
          <div class="bg-gray-50 h-56 rounded-t-xl">
            <iframe
              class="rounded-t-xl w-full h-full"
              src="https://www.youtube.com/embed/ih2vde4EOm8"
              title="Ученица 3 месяца обучения"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerpolicy="strict-origin-when-cross-origin"
              allowfullscreen
            ></iframe>
          </div>
          <div class="h-56 p-4">
            <div class="text-xl font-bold">Ученица первого курса:</div>
            <div class="leading-6 mt-3">
              “Меня записала мама, потому что она хотела, чтобы я куда-то
              развивалась и не лежала летом дома, не ленилась, чем-то могла себя
              занять и не гнила у себя в комнате в четырех стенах”
            </div>
          </div>
        </div>
        <div class="shadow-md rounded-xl">
          <div class="bg-gray-50 h-56 rounded-t-xl">
            <iframe
              class="w-full h-full rounded-t-xl"
              src="https://www.youtube.com/embed/GtVHOBP-V3U"
              title="Ученица 10 месяца обучения"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerpolicy="strict-origin-when-cross-origin"
              allowfullscreen
            ></iframe>
          </div>
          <div class="h-56 p-4">
            <div class="text-xl font-bold">Ученица второго курса:</div>
            <div class="leading-6 mt-3">
              “Начинала я с полного нуля. До этого я не пользовалась компьютером
              или ноутбуком. Максимум могла там, сделать маленький доклад и то,
              с большой помощью моей мамы”
            </div>
          </div>
        </div>
        <div class="shadow-md rounded-xl">
          <div class="bg-gray-50 h-56 rounded-t-xl">
            <iframe
              class="w-full h-full rounded-t-xl"
              src="https://www.youtube.com/embed/Yae_0Cfo6n8"
              title="Выпускник программирования"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerpolicy="strict-origin-when-cross-origin"
              allowfullscreen
            ></iframe>
          </div>
          <div class="h-56 p-4">
            <div class="text-xl font-bold">Выпускник:</div>
            <div class="leading-6 mt-3">
              “Все знания, которые давались в IT-школе, давались легко, потому
              что подавались такими порциями, поэтапно и не было никакой каши в
              голове.”
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="w-full mt-32 mb-32">
    <div class="max-w-screen-xl mx-auto p-1">
      <div class="grid lg:grid-cols-2 grid-cols-1 gap-x-5 gap-y-5">
        <div
          class="h-96 flex items-center justify-center"
          style="
            background-color: #8ec5fc;
            background-image: linear-gradient(62deg, #8ec5fc 0%, #e0c3fc 100%);
            border-radius: 60% 5% 5% 5%;
          "
        >
          <div class="text-white text-[180px] font-bold">70%</div>
        </div>
        <div class="lg:p-10 p-2 h-96 flex items-center gap-y-14">
          <div>
            <div class="text-4xl font-medium">
              Наших учеников определяются с
              <span class="text-cyan-500">будущей профессией</span> в айти
            </div>
            <div class="mt-5 text-xl">
              Они начинают с увлекательных проектов, которые помогают раскрыть
              их потенциал и интерес к технологиям. Наши курсы создают прочную
              основу для выбора будущей карьеры, развивая логическое мышление и
              навыки решения реальных задач
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="w-full mt-32 mb-32">
    <div class="max-w-screen-xl mx-auto p-1">
      <div class="grid lg:grid-cols-2 grid-cols-1 gap-x-14 gap-y-5">
        <div class="shadow-purple-400 shadow h-72 rounded-2xl sm:p-10 p-3">
          <div>
            <h1 class="text-3xl font-bold">Стоимость обучения</h1>
            <hr />
            <div class="mt-4">
              <ol class="text-xl font-medium">
                <li>Оффлайн: <span class="font-mono">30 000 ₸</span></li>
                <li>Онлайн: <span class="font-mono">28 000 ₸</span></li>
              </ol>
              <div class="mt-4 text-base">
                <div>Оплата курсов ежемесячная</div>
                *стоимость может варьироваться в зависимости от филиала
              </div>
            </div>
          </div>
        </div>
        <div class="shadow-blue-400 shadow h-72 rounded-2xl sm:p-10 p-3">
          <div>
            <h1 class="text-3xl font-bold flex items-center gap-x-2">
              Наши филиалы
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="currentColor"
                  class="bi bi-geo-alt-fill"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6"
                  />
                </svg>
              </span>
            </h1>
            <hr />
            <div class="mt-4">
              <ol class="  ">
                <li>
                  г. Алматы, Ауэзовский район, Жубанова 94а,
                  <a
                    href="https://www.instagram.com/iteasy.school/"
                    class="text-blue-500"
                    >Instagram</a
                  >
                </li>
                <li>
                  г. Актау, 13-й микрорайон, 55,
                  <a
                    href="https://www.instagram.com/it.school_aktau/"
                    class="text-blue-500"
                    >Instagram</a
                  >
                </li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    class="w-full pb-20"
    style="
      background-color: #21d4fd;
      background-image: linear-gradient(120deg, #21d4fd 0%, #b721ff 100%);
    "
  >
    <div id="form"></div>
    <div class="max-w-screen-xl mx-auto p-1">
      <div class="flex justify-center">
        <div class="w-[550px] sm:px-15 px-4">
          <div class="text-4xl text-white font-bold text-center mt-20">
            Записаться на <span class="text-cyan-300">бесплатный</span> пробный
            урок
          </div>

          <!-- Success Message -->
          <div *ngIf="enrollmentMessage" class="mt-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
            {{ enrollmentMessage }}
          </div>

          <!-- Error Message -->
          <div *ngIf="enrollmentError" class="mt-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
            {{ enrollmentError }}
          </div>

          <form [formGroup]="enrollmentForm" (ngSubmit)="onEnrollmentSubmit()">
            <div class="mt-12">
              <input
                type="text"
                placeholder="Напишите имя"
                class="w-full placeholder-gray-500 outline-none px-2 rounded-md py-3"
                [class.border-red-500]="isEnrollmentFieldInvalid('name')"
                formControlName="name"
              />
              <div *ngIf="isEnrollmentFieldInvalid('name')" class="text-red-300 text-sm mt-1">
                {{ getEnrollmentFieldError('name') }}
              </div>
            </div>
            <div class="mt-5">
              <app-phone-input
                formControlName="phone"
                [showError]="isEnrollmentFieldInvalid('phone')"
                [errorMessage]="getEnrollmentFieldError('phone')"
                [required]="true"
                [showLabel]="false"
                placeholder="Телефон с WhatsApp для связи">
              </app-phone-input>
            </div>
            <div class="mt-5">
              <button
                type="submit"
                [disabled]="isSubmittingEnrollment"
                class="w-full rounded-md hover:underline transition-all hover:bg-purple-600 py-3 bg-purple-800 text-white text-lg font-bold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span *ngIf="isSubmittingEnrollment" class="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                {{ isSubmittingEnrollment ? 'Отправка...' : 'Записаться' }}
              </button>
            </div>
          </form>
          <div class="mt-5">
            <a
              href="https://wa.me/77787734444"
              class="w-full backdrop-blur-sm bg-green-400 rounded-md hover:underline transition-all hover:bg-green-500 cursor-pointer duration-300 py-3 text-white text-lg font-bold flex justify-center items-center gap-x-4"
            >
              <span>Написать нам в Whatsapp</span>
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="28"
                  height="28"
                  fill="currentColor"
                  class="bi bi-whatsapp"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232"
                  />
                </svg>
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="w-full 32 mb-10">
    <div class="max-w-screen-xl mx-auto p-1">
      <div id="faq"></div>
      <h3 class="text-center font-bold text-2xl mt-32 mb-12">
        Вопросы и ответы
      </h3>
      <div class="text-lg lg:px-52 px-4">
        <div class="accordion-container">
          <div
            *ngFor="let item of faqItems; trackBy: trackByFaqId"
            class="accordion-item"
          >
            <h2 [id]="'accordion-heading-' + item.id">
              <button
                type="button"
                class="flex items-center justify-between w-full py-5 font-medium rtl:text-right text-black border-b border-gray-200 gap-3 hover:text-gray-700 transition-colors duration-200"
                [attr.aria-expanded]="item.isOpen"
                [attr.aria-controls]="'accordion-body-' + item.id"
                (click)="toggleAccordion(item.id)"
              >
                <span class="text-left">{{ item.question }}</span>
                <svg
                  class="w-3 h-3 shrink-0 transition-transform duration-200"
                  [class.rotate-180]="item.isOpen"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 10 6"
                >
                  <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5 5 1 1 5"
                  />
                </svg>
              </button>
            </h2>
            <div
              [id]="'accordion-body-' + item.id"
              [class.hidden]="!item.isOpen"
              [attr.aria-labelledby]="'accordion-heading-' + item.id"
              class="accordion-content transition-all duration-300 ease-in-out"
            >
              <div class="py-5 border-b border-gray-200 dark:border-gray-700">
                <p class="mb-2 text-gray-800 ">
                  {{ item.answer }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center font-medium text-xl mt-20 lg:px-72 px-3">
        Если у вас остались вопросы, вы можете написать нам в наших социальных
        сетях ↓
        <div class="flex justify-center gap-10 pt-5 text-base">
          <a
            href="https://www.instagram.com/iteasy.school/"
            class="flex justify-center items-center gap-3"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="28"
              height="28"
              fill="currentColor"
              class="bi bi-instagram"
              viewBox="0 0 16 16"
            >
              <path
                d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.9 3.9 0 0 0-1.417.923A3.9 3.9 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.9 3.9 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.9 3.9 0 0 0-.923-1.417A3.9 3.9 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599s.453.546.598.92c.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.5 2.5 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.5 2.5 0 0 1-.92-.598 2.5 2.5 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233s.008-2.388.046-3.231c.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92s.546-.453.92-.598c.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92m-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217m0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334"
              />
            </svg>
            iteasy.school
          </a>
          <a
            href="https://wa.me/77787734444"
            class="flex justify-center items-center gap-3"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="28"
              height="28"
              fill="currentColor"
              class="bi bi-whatsapp"
              viewBox="0 0 16 16"
            >
              <path
                d="M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232"
              />
            </svg>
            +7 (778) 773 44 44
          </a>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- Contact Popup -->
<app-contact-popup></app-contact-popup>
