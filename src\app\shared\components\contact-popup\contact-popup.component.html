<!-- Contact Popup -->
<div *ngIf="isVisible" class="fixed bottom-4 right-4 z-50 popup-wrapper" [@fadeIn]>
  <!-- Operator Icon -->
  <div
    *ngIf="!isExpanded"
    [@iconAnimation]
    class="relative cursor-pointer group contact-icon"
    (click)="togglePopup()">

    <!-- Main <PERSON> -->
    <div class="relative bg-blue-500 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg">
      <!-- Phone Icon -->
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-telephone-fill" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/>
      </svg>
    </div>

    <!-- Tooltip -->
    <div class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
      Оставьте заявку и мы свяжемся с вами
      <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
    </div>
  </div>

  <!-- Expanded State - Contact Form -->
  <div
    *ngIf="isExpanded"
    [@slideUpIn]
    class="absolute bottom-0 right-0 bg-white rounded-lg border border-gray-200 w-80 max-w-sm popup-container">
    
    <!-- Header -->
    <div class="bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <!-- Operator Avatar -->
        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-person-fill" viewBox="0 0 16 16">
            <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6"/>
          </svg>
        </div>
        <div>
          <div class="font-semibold text-sm">Оператор ITeasy</div>
          <div class="text-xs text-blue-200">Онлайн</div>
        </div>
      </div>
      <div class="flex space-x-2"> 
        <button
          (click)="closePopup()"
          class="text-blue-200 hover:text-white transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
            <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Content -->
    <div class="p-4">
      <div class="mb-4">
        <p class="text-gray-700 text-sm">
          Привет! Оставьте свои контакты и мы свяжемся с вами в течении 5 минут.
        </p>
      </div>

      <!-- Success Message -->
      <div *ngIf="submitMessage" class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-sm">
        {{ submitMessage }}
      </div>

      <!-- Error Message -->
      <div *ngIf="submitError" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
        {{ submitError }}
      </div>

      <!-- Form -->
      <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" *ngIf="!submitMessage">
        <div class="mb-3">
          <app-phone-input
            formControlName="phone"
            [showError]="isFieldInvalid('phone')"
            [errorMessage]="getFieldError('phone')"
            [required]="true">
          </app-phone-input>
        </div>

        <div class="mb-4">
          <textarea
            placeholder="Ваше сообщение"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
            [class.border-red-500]="isFieldInvalid('message')"
            [class.bg-gray-100]="contactForm.get('message')?.disabled"
            [class.cursor-not-allowed]="contactForm.get('message')?.disabled"
            formControlName="message"
          ></textarea>
          <div *ngIf="isFieldInvalid('message')" class="text-red-500 text-xs mt-1">
            {{ getFieldError('message') }}
          </div>
          <div *ngIf="contactForm.get('message')?.disabled" class="text-gray-500 text-xs mt-1">
            Сначала введите номер телефона
          </div>
        </div>

        <button
          type="submit"
          [disabled]="isSubmitting || contactForm.invalid"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          <span *ngIf="isSubmitting" class="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
          {{ isSubmitting ? 'Отправка...' : 'Отправить сообщение' }}
        </button>
      </form>

      <!-- Quick Actions -->
      <div class="mt-4 pt-3 border-t border-gray-200" *ngIf="!submitMessage">
        <div class="text-xs text-gray-500 mb-2">Или свяжитесь с нами напрямую:</div>
        <div class="flex space-x-2">
          <a 
            href="https://wa.me/77787734444"
            class="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-3 rounded-md text-xs font-medium transition-colors duration-200 flex items-center justify-center space-x-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-whatsapp" viewBox="0 0 16 16">
              <path d="M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232"/>
            </svg>
            <span>WhatsApp</span>
          </a>
          <a 
            href="tel:+77787734444"
            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-md text-xs font-medium transition-colors duration-200 flex items-center justify-center space-x-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-telephone-fill" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/>
            </svg>
            <span>Звонок</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
