import { Injectable } from '@angular/core';

export interface Country {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
  phoneLength: number; // Expected phone number length without country code
  phoneMask?: string; // Optional phone mask for formatting
}

@Injectable({
  providedIn: 'root'
})
export class CountryService {
  private countries: Country[] = [
    {
      code: 'KZ',
      name: 'Казахстан',
      dialCode: '+7',
      flag: 'assets/img/flags/kazakhstan.png',
      phoneLength: 10,
      phoneMask: '(999) 999-99-99'
    },
    {
      code: 'KG',
      name: 'Кыргызстан',
      dialCode: '+996',
      flag: 'assets/img/flags/kyrgyzstan.png',
      phoneLength: 9,
      phoneMask: '(999) 999-999'
    },
    {
      code: 'RU',
      name: 'Россия',
      dialCode: '+7',
      flag: 'assets/img/flags/russia.png',
      phoneLength: 10,
      phoneMask: '(999) 999-99-99'
    },
    {
      code: 'UZ',
      name: 'Узбекистан',
      dialCode: '+998',
      flag: 'assets/img/flags/uzbekistan.png',
      phoneLength: 9,
      phoneMask: '(99) 999-99-99'
    },
    {
      code: 'TJ',
      name: 'Таджикистан',
      dialCode: '+992',
      flag: 'assets/img/flags/tajikistan.png',
      phoneLength: 9,
      phoneMask: '(99) 999-99-99'
    },
    {
      code: 'TM',
      name: 'Туркменистан',
      dialCode: '+993',
      flag: 'assets/img/flags/turkmenistan.png',
      phoneLength: 8,
      phoneMask: '(99) 99-99-99'
    },
    {
      code: 'BY',
      name: 'Беларусь',
      dialCode: '+375',
      flag: 'assets/img/flags/belarus.png',
      phoneLength: 9,
      phoneMask: '(99) 999-99-99'
    },
    {
      code: 'UA',
      name: 'Украина',
      dialCode: '+380',
      flag: 'assets/img/flags/ukraine.png',
      phoneLength: 9,
      phoneMask: '(99) 999-99-99'
    },
    {
      code: 'AZ',
      name: 'Азербайджан',
      dialCode: '+994',
      flag: 'assets/img/flags/azerbaijan.png',
      phoneLength: 9,
      phoneMask: '(99) 999-99-99'
    },
    {
      code: 'AM',
      name: 'Армения',
      dialCode: '+374',
      flag: 'assets/img/flags/armenia.png',
      phoneLength: 8,
      phoneMask: '(99) 99-99-99'
    },
    {
      code: 'GE',
      name: 'Грузия',
      dialCode: '+995',
      flag: 'assets/img/flags/georgia.png',
      phoneLength: 9,
      phoneMask: '(999) 99-99-99'
    },
    {
      code: 'MD',
      name: 'Молдова',
      dialCode: '+373',
      flag: 'assets/img/flags/moldova.png',
      phoneLength: 8,
      phoneMask: '(99) 99-99-99'
    }
  ];

  getCountries(): Country[] {
    return this.countries;
  }

  getCountryByCode(code: string): Country | undefined {
    return this.countries.find(country => country.code === code);
  }

  getCountryByDialCode(dialCode: string): Country | undefined {
    return this.countries.find(country => country.dialCode === dialCode);
  }

  getDefaultCountry(): Country {
    return this.countries[0]; // Kazakhstan as default
  }
}
