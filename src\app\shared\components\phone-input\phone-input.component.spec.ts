import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { PhoneInputComponent } from './phone-input.component';
import { CountryService } from '../../../core/services/country.service';

describe('PhoneInputComponent', () => {
  let component: PhoneInputComponent;
  let fixture: ComponentFixture<PhoneInputComponent>;
  let countryService: CountryService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PhoneInputComponent],
      imports: [ReactiveFormsModule],
      providers: [CountryService]
    }).compileComponents();

    fixture = TestBed.createComponent(PhoneInputComponent);
    component = fixture.componentInstance;
    countryService = TestBed.inject(CountryService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default country (Kazakhstan)', () => {
    expect(component.selectedCountry.code).toBe('KZ');
    expect(component.selectedCountry.dialCode).toBe('+7');
  });

  it('should validate phone number correctly', () => {
    const control = new FormControl('+77771234567');
    const result = component.validate(control);
    expect(result).toBeNull();
  });

  it('should return error for invalid phone number', () => {
    const control = new FormControl('+7777123');
    const result = component.validate(control);
    expect(result).toEqual({ invalidPhone: true });
  });

  it('should return error for required field when empty', () => {
    component.required = true;
    const control = new FormControl('');
    const result = component.validate(control);
    expect(result).toEqual({ required: true });
  });

  it('should parse phone number correctly', () => {
    component.writeValue('+77771234567');
    expect(component.selectedCountry.code).toBe('KZ');
    expect(component.phoneNumber).toBe('7771234567');
  });

  it('should select country correctly', () => {
    const kyrgyzstan = countryService.getCountryByCode('KG');
    if (kyrgyzstan) {
      const mockEvent = new Event('click');
      component.selectCountry(kyrgyzstan, mockEvent);
      expect(component.selectedCountry.code).toBe('KG');
      expect(component.selectedCountry.dialCode).toBe('+996');
    }
  });

  it('should format phone number with mask', () => {
    // Test private method through public interface
    component.selectedCountry = countryService.getCountryByCode('KZ')!;
    const mockEvent = {
      target: { value: '7771234567' }
    };
    component.onPhoneInput(mockEvent);
    expect(component.phoneNumber).toContain('777');
  });

  it('should toggle dropdown', () => {
    const mockEvent = new Event('click');
    expect(component.isDropdownOpen).toBeFalsy();
    component.toggleDropdown(mockEvent);
    expect(component.isDropdownOpen).toBeTruthy();
  });

  it('should get full phone number', () => {
    component.selectedCountry = countryService.getCountryByCode('KZ')!;
    component.phoneNumber = '7771234567';
    const fullNumber = component.getFullPhoneNumber();
    expect(fullNumber).toBe('+77771234567');
  });

  it('should handle blur event', () => {
    component.onBlur();
    expect(component.isTouched).toBeTruthy();
  });
});
