# JWT Authentication Implementation

This document describes the JWT authentication system implemented for the ITeasy Studio admin panel.

## Overview

The authentication system provides secure login functionality for administrators to access the admin dashboard. It uses JWT tokens for authentication and includes proper route protection.

## Features

- JWT-based authentication
- Secure token storage in localStorage
- Route guards for protected routes
- Automatic token refresh capability
- User session management
- Responsive login form
- Admin dashboard with user information

## API Configuration

The system is configured to work with the Django backend API:

- **Base URL**: Configurable via environment variables (default: `http://127.0.0.1:8000`)
- **Login Endpoint**: `/api/auth/login/`
- **Token Refresh Endpoint**: `/api/auth/token/refresh/`

### Environment Configuration

Environment variables are configured in:
- `src/environments/environment.ts` (development)
- `src/environments/environment.prod.ts` (production)

```typescript
export const environment = {
  production: false,
  apiBaseUrl: 'http://127.0.0.1:8000'
};
```

## Authentication Flow

1. **Login**: User enters credentials on `/login` page
2. **Token Storage**: Upon successful authentication, JWT tokens are stored in localStorage
3. **Route Protection**: Protected routes check authentication status via AuthGuard
4. **Dashboard Access**: Authenticated users can access `/dashboard`
5. **Logout**: Clears tokens and redirects to login page

## API Request/Response Format

### Login Request
```json
{
  "username": "baika",
  "password": "qweqwe123123"
}
```

### Login Response
```json
{
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "user_id": 2,
    "username": "baika",
    "email": "",
    "role": "user"
  }
}
```

## File Structure

```
src/app/
├── core/
│   ├── guards/
│   │   └── auth.guard.ts
│   ├── models/
│   │   └── auth.models.ts
│   └── services/
│       └── auth.service.ts
├── features/
│   ├── auth/
│   │   └── components/
│   │       └── login/
│   │           ├── login.component.ts
│   │           ├── login.component.html
│   │           └── login.component.css
│   └── admin/
│       └── components/
│           └── dashboard/
│               ├── dashboard.component.ts
│               ├── dashboard.component.html
│               └── dashboard.component.css
└── environments/
    ├── environment.ts
    └── environment.prod.ts
```

## Routes

- `/` - Main page (public)
- `/studio` - Studio page (public)
- `/login` - Login page (public)
- `/dashboard` - Admin dashboard (protected)

## Usage

### Testing the Authentication

1. Start the development server: `ng serve`
2. Navigate to `http://localhost:4200/login`
3. Enter credentials:
   - Username: `baika`
   - Password: `qweqwe123123`
4. Upon successful login, you'll be redirected to `/dashboard`
5. Try accessing `/dashboard` directly without logging in - you'll be redirected to `/login`

### Logout

Click the "Выйти" (Logout) button in the dashboard header to log out and return to the login page.

## Security Features

- JWT tokens are stored securely in localStorage
- Route guards prevent unauthorized access to protected routes
- Automatic token refresh capability (when backend supports it)
- Form validation on login inputs
- Error handling for various authentication scenarios

## Customization

To change the backend URL, update the `apiBaseUrl` in the environment files:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiBaseUrl: 'https://your-backend-url.com'
};
```

## Dependencies

The authentication system uses the following Angular modules:
- `@angular/common/http` - HTTP client for API calls
- `@angular/forms` - Reactive forms for login form
- `@angular/router` - Routing and navigation
- `rxjs` - Reactive programming with observables
