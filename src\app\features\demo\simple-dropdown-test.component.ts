import { Component } from '@angular/core';
import { CountryService, Country } from '../../core/services/country.service';

@Component({
  selector: 'app-simple-dropdown-test',
  template: `
    <div class="p-8">
      <h2 class="text-xl mb-4">Simple Dropdown Test</h2>
      
      <div class="relative inline-block">
        <button 
          type="button"
          class="bg-blue-500 text-white px-4 py-2 rounded"
          (click)="toggleDropdown()">
          {{ selectedCountry.name }} {{ selectedCountry.dialCode }}
          <span class="ml-2">{{ isOpen ? '▲' : '▼' }}</span>
        </button>
        
        <div 
          *ngIf="isOpen"
          class="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-[200px]">
          <div 
            *ngFor="let country of countries"
            class="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center gap-2"
            (click)="selectCountry(country)">
            <img [src]="country.flag" [alt]="country.name" class="w-5 h-4 object-cover">
            <span>{{ country.dialCode }}</span>
            <span>{{ country.name }}</span>
          </div>
        </div>
      </div>
      
      <div class="mt-4 text-sm">
        <p>Is Open: {{ isOpen }}</p>
        <p>Selected: {{ selectedCountry.name }}</p>
        <p>Countries loaded: {{ countries.length }}</p>
      </div>
    </div>
  `,
  standalone: false
})
export class SimpleDropdownTestComponent {
  isOpen = false;
  countries: Country[] = [];
  selectedCountry: Country;

  constructor(private countryService: CountryService) {
    this.countries = this.countryService.getCountries();
    this.selectedCountry = this.countryService.getDefaultCountry();
    console.log('SimpleDropdownTest loaded countries:', this.countries);
  }

  toggleDropdown() {
    this.isOpen = !this.isOpen;
    console.log('Dropdown toggled:', this.isOpen);
  }

  selectCountry(country: Country) {
    this.selectedCountry = country;
    this.isOpen = false;
    console.log('Country selected:', country.name);
  }
}
