import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { ContactPopupComponent } from './components/contact-popup/contact-popup.component';
import { PhoneInputComponent } from './components/phone-input/phone-input.component';

@NgModule({
  declarations: [
    ContactPopupComponent,
    PhoneInputComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  exports: [
    ContactPopupComponent,
    PhoneInputComponent
  ]
})
export class SharedModule { }
