# Studio Requests Implementation

This document describes the studio requests system implemented for the ITeasy Studio application.

## Overview

The studio requests system allows users to submit project requests through the studio page and enables administrators to view and manage these requests through the admin dashboard.

## Features

- **Public Request Submission**: Users can submit requests via the studio page contact form
- **Admin Request Management**: Administrators can view all requests in the dashboard
- **Status Management**: Requests can have statuses: 'new', 'in_progress', 'done'
- **Form Validation**: Client-side validation for all form fields
- **Real-time Updates**: Dashboard displays real-time request data
- **Responsive Design**: Works on all device sizes

## API Endpoints

### Get All Requests (Admin Only)
```
GET /api/studio-requests/
Authorization: Bearer <access_token>
```

**Response:**
```json
[
  {
    "id": 2,
    "name": "<PERSON>2",
    "phone": "+1234567890",
    "message": "Update message",
    "created_at": "2025-06-21T18:59:53.076109+05:00",
    "status": "in_progress"
  },
  {
    "id": 1,
    "name": "<PERSON>",
    "phone": "+1234567890",
    "message": "Update message",
    "created_at": "2025-06-21T18:59:18.689322+05:00",
    "status": "in_progress"
  }
]
```

### Create New Request (Public)
```
POST /api/studio-requests/
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "John Smith2",
  "phone": "+1234567890",
  "message": "Update message",
  "status": "new"
}
```

**Response:**
```json
{
  "id": 3,
  "name": "John Smith2",
  "phone": "+1234567890",
  "message": "Update message",
  "created_at": "2025-06-21T19:15:30.123456+05:00",
  "status": "new"
}
```

## File Structure

```
src/app/
├── core/
│   ├── models/
│   │   └── studio-request.models.ts
│   └── services/
│       └── studio-requests.service.ts
├── features/
│   ├── admin/
│   │   └── components/
│   │       └── dashboard/
│   │           ├── dashboard.component.ts (updated)
│   │           └── dashboard.component.html (updated)
│   └── studio/
│       └── components/
│           └── studio-page/
│               ├── studio-page.component.ts (updated)
│               ├── studio-page.component.html (updated)
│               └── studio-page.component.css (updated)
```

## Models

### StudioRequest Interface
```typescript
export interface StudioRequest {
  id: number;
  name: string;
  phone: string;
  message: string;
  created_at: string;
  status: 'new' | 'in_progress' | 'done';
}
```

### CreateStudioRequest Interface
```typescript
export interface CreateStudioRequest {
  name: string;
  phone: string;
  message: string;
  status?: 'new' | 'in_progress' | 'done';
}
```

## Service Methods

### StudioRequestsService

- `getAllRequests()`: Fetches all requests (requires authentication)
- `createRequest(request)`: Creates a new request (public endpoint)
- `updateRequest(id, request)`: Updates an existing request (requires authentication)
- `deleteRequest(id)`: Deletes a request (requires authentication)

## Form Validation

### Studio Page Contact Form

- **Name**: Required, minimum 2 characters
- **Phone**: Required, valid phone number format (+X XXXXXXXXXX)
- **Message**: Required, minimum 10 characters

### Validation Messages

- Russian language error messages
- Real-time validation feedback
- Visual error indicators (red borders)

## Dashboard Features

### Request Display

- **Table View**: All requests displayed in a responsive table
- **Status Badges**: Color-coded status indicators
- **Date Formatting**: Localized date/time display
- **Status Updates**: Dropdown to change request status
- **Refresh Button**: Manual refresh with loading indicator

### Status Management

- **New**: Blue badge - newly submitted requests
- **In Progress**: Yellow badge - requests being worked on
- **Done**: Green badge - completed requests

## Usage

### Submitting a Request (Studio Page)

1. Navigate to `/studio`
2. Scroll to the contact section
3. Fill out the form:
   - Enter your name (minimum 2 characters)
   - Enter phone number (valid format)
   - Describe your project (minimum 10 characters)
4. Click "Отправить заявку"
5. Success message will appear upon successful submission

### Managing Requests (Admin Dashboard)

1. Login to admin panel at `/login`
2. Navigate to dashboard at `/dashboard`
3. View all requests in the "Заявки студии" section
4. Use the status dropdown to update request status
5. Click refresh button to reload requests

## Error Handling

### Client-Side Errors

- Form validation errors with specific messages
- Network connection errors
- Server response errors

### Server-Side Errors

- 400: Bad Request - validation errors
- 401: Unauthorized - authentication required
- 403: Forbidden - insufficient permissions
- 500: Server Error - backend issues

## Security

- **Authentication Required**: Admin endpoints require JWT token
- **Input Validation**: All form inputs are validated
- **CORS Handling**: Proper cross-origin request handling
- **Error Sanitization**: Safe error message display

## Testing

### Manual Testing Steps

1. **Submit Request**:
   - Go to studio page
   - Fill and submit contact form
   - Verify success message

2. **View Requests**:
   - Login to admin dashboard
   - Check requests table
   - Verify request appears

3. **Update Status**:
   - Change request status via dropdown
   - Verify status updates immediately

4. **Form Validation**:
   - Try submitting empty form
   - Verify validation messages appear
   - Test invalid phone numbers

## Configuration

The service uses the same environment configuration as the authentication system:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiBaseUrl: 'http://127.0.0.1:8000'
};
```

## Dependencies

- `@angular/forms`: Reactive forms for validation
- `@angular/common/http`: HTTP client for API calls
- `rxjs`: Reactive programming with observables
