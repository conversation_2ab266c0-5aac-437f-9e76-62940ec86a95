import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { StudioRequest, CreateStudioRequest } from '../models/studio-request.models';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class StudioRequestsService {
  private readonly API_URL = environment.apiBaseUrl;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Get all studio requests (for admin dashboard)
   */
  getAllRequests(): Observable<StudioRequest[]> {
    const headers = this.getAuthHeaders();
    
    return this.http.get<StudioRequest[]>(
      `${this.API_URL}/api/studio-requests/`,
      { headers }
    ).pipe(
      tap(requests => {
        console.log('Fetched studio requests:', requests);
      }),
      catchError(error => {
        console.error('Error fetching studio requests:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new studio request (public endpoint for studio page)
   */
  createRequest(request: CreateStudioRequest): Observable<StudioRequest> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    // Set default status if not provided
    const requestData = {
      ...request,
      status: request.status || 'new'
    };

    return this.http.post<StudioRequest>(
      `${this.API_URL}/api/studio-requests/`,
      requestData,
      { headers }
    ).pipe(
      tap(response => {
        console.log('Studio request created:', response);
      }),
      catchError(error => {
        console.error('Error creating studio request:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Update a studio request (for admin use)
   */
  updateRequest(id: number, request: Partial<StudioRequest>): Observable<StudioRequest> {
    const headers = this.getAuthHeaders();

    return this.http.patch<StudioRequest>(
      `${this.API_URL}/api/studio-requests/${id}/`,
      request,
      { headers }
    ).pipe(
      tap(response => {
        console.log('Studio request updated:', response);
      }),
      catchError(error => {
        console.error('Error updating studio request:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a studio request (for admin use)
   */
  deleteRequest(id: number): Observable<void> {
    const headers = this.getAuthHeaders();

    return this.http.delete<void>(
      `${this.API_URL}/api/studio-requests/${id}/`,
      { headers }
    ).pipe(
      tap(() => {
        console.log('Studio request deleted:', id);
      }),
      catchError(error => {
        console.error('Error deleting studio request:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get authorization headers for authenticated requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAccessToken();
    
    if (!token) {
      throw new Error('No access token available');
    }

    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  }
}
