import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { Message, CreateMessage } from '../models/studio-request.models';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class MessagesService {
  private readonly API_URL = environment.apiBaseUrl;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Get all messages (for admin dashboard)
   */
  getAllMessages(): Observable<Message[]> {
    const headers = this.getAuthHeaders();
    
    return this.http.get<Message[]>(
      `${this.API_URL}/api/messages/`,
      { headers }
    ).pipe(
      tap(messages => {
        console.log('Fetched messages:', messages);
      }),
      catchError(error => {
        console.error('Error fetching messages:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new message (public endpoint for contact popup)
   */
  createMessage(message: CreateMessage): Observable<Message> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<Message>(
      `${this.API_URL}/api/messages/`,
      message,
      { headers }
    ).pipe(
      tap(response => {
        console.log('Message created:', response);
      }),
      catchError(error => {
        console.error('Error creating message:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get a specific message (for admin use)
   */
  getMessage(id: number): Observable<Message> {
    const headers = this.getAuthHeaders();

    return this.http.get<Message>(
      `${this.API_URL}/api/messages/${id}/`,
      { headers }
    ).pipe(
      tap(response => {
        console.log('Message fetched:', response);
      }),
      catchError(error => {
        console.error('Error fetching message:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Update a message (for admin use)
   */
  updateMessage(id: number, message: Partial<Message>): Observable<Message> {
    const headers = this.getAuthHeaders();

    return this.http.patch<Message>(
      `${this.API_URL}/api/messages/${id}/`,
      message,
      { headers }
    ).pipe(
      tap(response => {
        console.log('Message updated:', response);
      }),
      catchError(error => {
        console.error('Error updating message:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a message (for admin use)
   */
  deleteMessage(id: number): Observable<void> {
    const headers = this.getAuthHeaders();

    return this.http.delete<void>(
      `${this.API_URL}/api/messages/${id}/`,
      { headers }
    ).pipe(
      tap(() => {
        console.log('Message deleted:', id);
      }),
      catchError(error => {
        console.error('Error deleting message:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get authorization headers for authenticated requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAccessToken();
    
    if (!token) {
      throw new Error('No access token available');
    }

    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  }
}
