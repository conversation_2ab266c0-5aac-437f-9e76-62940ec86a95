# School Enrollment Implementation

This document describes the school enrollment system implemented for the ITeasy Studio application.

## Overview

The school enrollment system allows users to register for free trial lessons through the main page enrollment form and enables administrators to view and manage these enrollments through the admin dashboard.

## Features

- **Public Enrollment Submission**: Users can submit enrollment requests via the main page form
- **Admin Enrollment Management**: Administrators can view all enrollments in the dashboard
- **Form Validation**: Client-side validation for all form fields
- **Real-time Updates**: Dashboard displays real-time enrollment data
- **Responsive Design**: Works on all device sizes
- **Simple Data Model**: Only requires name and phone number

## API Endpoints

### Get All Enrollments (Admin Only)
```
GET /api/school-enrollments/
Authorization: Bearer <access_token>
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "<PERSON>",
    "phone": "+1234567890",
    "created_at": "2025-06-21T19:14:17.416912+05:00"
  }
]
```

### Create New Enrollment (Public)
```
POST /api/school-enrollments/
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "<PERSON>",
  "phone": "+1234567890"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Alice Smith",
  "phone": "+1234567890",
  "created_at": "2025-06-21T19:20:15.123456+05:00"
}
```

## File Structure

```
src/app/
├── core/
│   ├── models/
│   │   └── school-enrollment.models.ts
│   └── services/
│       └── school-enrollment.service.ts
├── features/
│   ├── admin/
│   │   └── components/
│   │       └── dashboard/
│   │           ├── dashboard.component.ts (updated)
│   │           └── dashboard.component.html (updated)
│   └── main/
│       └── components/
│           └── main-component/
│               ├── main-component.ts (updated)
│               └── main-component.html (updated)
```

## Models

### SchoolEnrollment Interface
```typescript
export interface SchoolEnrollment {
  id: number;
  name: string;
  phone: string;
  created_at: string;
}
```

### CreateSchoolEnrollment Interface
```typescript
export interface CreateSchoolEnrollment {
  name: string;
  phone: string;
}
```

## Service Methods

### SchoolEnrollmentService

- `getAllEnrollments()`: Fetches all enrollments (requires authentication)
- `createEnrollment(enrollment)`: Creates a new enrollment (public endpoint)
- `updateEnrollment(id, enrollment)`: Updates an existing enrollment (requires authentication)
- `deleteEnrollment(id)`: Deletes an enrollment (requires authentication)

## Form Validation

### Main Page Enrollment Form

- **Name**: Required, minimum 2 characters
- **Phone**: Required, valid phone number format (+X XXXXXXXXXX)

### Validation Messages

- Russian language error messages
- Real-time validation feedback
- Visual error indicators (red borders)

## Dashboard Features

### Enrollment Display

- **Table View**: All enrollments displayed in a responsive table
- **Date Formatting**: Localized date/time display
- **Refresh Button**: Manual refresh with loading indicator
- **Simple Layout**: ID, Name, Phone, Registration Date

## Usage

### Submitting an Enrollment (Main Page)

1. Navigate to the main page (`/`)
2. Scroll to the enrollment form section (blue gradient background)
3. Fill out the form:
   - Enter child's name (minimum 2 characters)
   - Enter phone number with WhatsApp (valid format)
4. Click "Записаться"
5. Success message will appear upon successful submission

### Managing Enrollments (Admin Dashboard)

1. Login to admin panel at `/login`
2. Navigate to dashboard at `/dashboard`
3. View all enrollments in the "Записи в школу" section
4. Click refresh button to reload enrollments

## Error Handling

### Client-Side Errors

- Form validation errors with specific messages
- Network connection errors
- Server response errors

### Server-Side Errors

- 400: Bad Request - validation errors
- 401: Unauthorized - authentication required
- 403: Forbidden - insufficient permissions
- 500: Server Error - backend issues

## Security

- **Authentication Required**: Admin endpoints require JWT token
- **Input Validation**: All form inputs are validated
- **CORS Handling**: Proper cross-origin request handling
- **Error Sanitization**: Safe error message display

## Testing

### Manual Testing Steps

1. **Submit Enrollment**:
   - Go to main page
   - Scroll to enrollment form (blue gradient section)
   - Fill and submit form
   - Verify success message

2. **View Enrollments**:
   - Login to admin dashboard
   - Check enrollments table
   - Verify enrollment appears

3. **Form Validation**:
   - Try submitting empty form
   - Verify validation messages appear
   - Test invalid phone numbers

## Integration with Main Page

The enrollment form is integrated into the existing main page design:

- **Location**: Blue gradient section with "Записаться на бесплатный пробный урок" heading
- **Design**: Matches existing form styling with white inputs and purple button
- **Responsive**: Works on mobile and desktop
- **Accessibility**: Proper form labels and error messages

## Configuration

The service uses the same environment configuration as other services:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiBaseUrl: 'http://127.0.0.1:8000'
};
```

## Dependencies

- `@angular/forms`: Reactive forms for validation
- `@angular/common/http`: HTTP client for API calls
- `rxjs`: Reactive programming with observables

## Differences from Studio Requests

- **Simpler Model**: Only name and phone (no message or status)
- **Different Purpose**: School enrollment vs project requests
- **Different Location**: Main page vs studio page
- **Simpler Management**: View-only in dashboard (no status updates)
