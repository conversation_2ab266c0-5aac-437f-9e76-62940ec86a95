import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Custom validator for international phone number validation
 * Validates phone numbers with country codes
 */
export function phoneValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const phoneValue = control.value.toString();
    // Remove all non-digit characters except +
    const cleanPhone = phoneValue.replace(/[^\d+]/g, '');

    // Basic validation - must start with + and have at least 10 digits
    const isValidFormat = /^\+\d{10,15}$/.test(cleanPhone);

    if (!isValidFormat) {
      return { invalidPhone: true };
    }

    return null;
  };
}

/**
 * Legacy validator for strict Russian phone number validation
 * Accepts only numbers starting with +7 or 8 and containing exactly 11 digits
 */
export function russianPhoneValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const phoneValue = control.value.toString();
    // Remove all non-digit characters except +
    const cleanPhone = phoneValue.replace(/[^\d+]/g, '');

    // Check if it starts with +7 or 8 and has exactly 11 digits
    const isValidFormat = /^(\+7|8)\d{10}$/.test(cleanPhone);

    if (!isValidFormat) {
      return { invalidPhone: true };
    }

    return null;
  };
}

/**
 * Format phone number input to ensure correct format
 * @param event Input event from phone field
 * @returns Formatted phone number
 */
export function formatPhoneInput(event: any): string {
  let value = event.target.value;
  
  // Remove all non-digit characters except +
  let cleanValue = value.replace(/[^\d+]/g, '');
  
  // If starts with 8, keep it as is
  // If starts with 7, add + prefix
  // If starts with +7, keep it as is
  if (cleanValue.startsWith('7') && !cleanValue.startsWith('+7')) {
    cleanValue = '+7' + cleanValue.substring(1);
  }
  
  // Limit to 12 characters for +7XXXXXXXXXX or 11 for 8XXXXXXXXXX
  if (cleanValue.startsWith('+7')) {
    cleanValue = cleanValue.substring(0, 12);
  } else if (cleanValue.startsWith('8')) {
    cleanValue = cleanValue.substring(0, 11);
  }
  
  return cleanValue;
}

/**
 * Get phone validation error message
 * @param errors Validation errors object
 * @returns Error message string
 */
export function getPhoneErrorMessage(errors: ValidationErrors | null): string {
  if (errors?.['required']) {
    return 'Это поле обязательно для заполнения';
  }
  if (errors?.['invalidPhone']) {
    return 'Введите корректный номер телефона';
  }
  if (errors?.['pattern']) {
    return 'Введите корректный номер телефона';
  }
  return '';
}

/**
 * Get Russian phone validation error message
 * @param errors Validation errors object
 * @returns Error message string
 */
export function getRussianPhoneErrorMessage(errors: ValidationErrors | null): string {
  if (errors?.['required']) {
    return 'Это поле обязательно для заполнения';
  }
  if (errors?.['invalidPhone']) {
    return 'Номер должен начинаться с +7 или 8 и содержать 11 цифр';
  }
  if (errors?.['pattern']) {
    return 'Введите корректный номер телефона';
  }
  return '';
}
